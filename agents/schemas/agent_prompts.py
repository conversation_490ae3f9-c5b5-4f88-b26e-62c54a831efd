
context_prompt = """
The given image is an image version of a page from a pdf. This is a part of an application which extracts MCQs from the given page.
"""

page_classifier_prompt = """
- Analyse the given image, it will have either Multiple choice questions or Answers keys (ANSWERS/SOLUTIONS) or Explanations
- Based on the analysis identify whether the page contains the following these strict rules:
    - MCQs (multiple-choice questions)
        - MC<PERSON> will have question number and question text followed by multiple choice options
        - A question is considered an MCQ if it follows this structure:
            <Question Number>. <Question Text>
            <Option 1> <Option 2> <Option 3> <Option 4>
    - Answers
        - Answers Key will have ONLY Question number and Correct option in a row and column.
        - Answer Key format:
            <Number> <Key>
        - It will be titled: ANSWERS/SOLUTIONS/ANSWER KEYS (Only then it is Answer Keys)
    - Explanations
        - Explanations will have Question number and explanation.
        - Explanations follow the format: <Explanation Number>. <Correct Option> <Explanation>.
        - Explanation content may include sub-points or lists using bullets, dots (•, .), or dashes (-). These are part of the explanation and should still be treated as explanations.
RETURN THE OUTPUT AS containsMCQs:true/false, containsAnswers:true/false, containsExplanations:true/false
- DO NOT use any markdown headings.
"""


question_page_classifier_prompt = """
Analyze the given text and extract the following details:

    - Identify ALL the Multiple Choice Questions (MCQs) in the given image.
    - Count the total number of MCQs present in the given image.
    - Identify the first MCQ's question number.
    - Identify the last MCQ's question number.
    - A question is considered an MCQ if it follows this structure:
    - A question number, followed by at least one answer choice in the format (1), (2), (3), (4).
    - Determine whether the last MCQ is incomplete (i.e., missing part of the question or answer choices).
    - Check if there is any content before the first MCQ (e.g., headings, instructions, or other text).
    - Check if there is any content before the first MCQ, excluding section headings and footers.
    - Ignore headings such as topic titles or section labels.
    - Consider only instructions, paragraphs, or explanatory text before the first MCQ.
    - DO NOT MISS ANY MCQ

    RETURN THE OUTPUT IN MARKDOWN FORMAT AS totalQuestions, startQuestionNumber, endQuestionNumber, hasIncompleteQuestions, contentBeforeFirstQuestion
    Once the response is generated please check again if the information you have provided is correct.
"""

explanation_page_classifier_prompt = """
This image contains explanations for MCQs. Identify and extract the following details:

    - Identify all explanations, ensuring only explanations are considered (not MCQs).
    - Explanations follow the format: <Question Number>. <Correct Option> (e.g., 43. (1)).
    - The explanation text may include multiple paragraphs or sub-points.
    - Sub-points may be formatted using bullets, dots (e.g., `•`, `.`), dashes, or list markers. These are part of the explanation and must be included in detection.
    - Count the total number of explanations present in the image.
    - Identify the question number of the first explanation.
    - Identify the question number of the last explanation.
    - Determine whether the last explanation is incomplete (i.e., missing either the answer choice or explanation text).
    - Check if there is any content before the first explanation (e.g., instructions, headings, or text from a previous page).
    - If the first explanation is not numbered properly, it is an overflow from the previous page and should be marked as incomplete.

RETURN THE OUTPUT IN MARKDOWN FORMAT AS:
    totalExplanations, startExplanationNumber, endExplanationNumber, hasIncompleteExplanations, contentBeforeFirstExplanation

Once the response is generated please check again if the information you have provided is correct.
"""


pattern_classifier = """
Analyse the Multiple Choice Questions (MCQs) in the given image accurately following these strict rules:
- Identify the Question pattern and Options pattern
    For example:
     {{
        "QuestionPattern": "1. ",
        "OptionPattern": "(1) "
    }}
- Return the output in a json format
"""


def answer_key_extractor_prompt() -> str:
    answer_key_prompt = """
    - Extract ALL Answer keys (titled under: ANSWERS/SOLUTIONS/ANSWER KEYS) from the image accurately following these rules:
    - Ensure Answer keys appear exactly as they are in the image.
    - Capture ONLY Answer keys (titled under: ANSWERS/SOLUTIONS/ANSWER KEYS)
    - Answers Key will have ONLY Question number and Correct option in a row and column layout.
    - Ensure Answer keys are extracted with extract question numbers as they are in the image.
    - Ensure to extract all answer keys exactly as they are in the image.
    - D0 NOT include multiple-choice questions or explanation questions. Give only answer key values.
    - Do NOT modify question wording, even if it appears incorrect.
    - Do NOT miss or modify any answer keys.
    - IF THE IMAGE DOES NOT CONTAIN ANY ANSWER KEY, IGNORE the image and return ""
    Example format:
    ANSWERS
    1. (1) 2. (3)
    Instructions:
    - DO NOT include Multiple-choice questions or Explanations.
    - DO NOT add any descriptive text, comments, or placeholders.
    - Maintain the format, layout.
    - Maintain the exact format, preserving spacing, line breaks, and layout.
    - Do NOT use any markdown headings.
    """
    return answer_key_prompt


def question_extractor_prompt() -> str:
    question_prompt = """
You are an expert MCQ extractor.
You will receive exactly one image as input.

- CRITICAL RULE:
    - **Do not infer, create, guess, generate, or assume any missing content.**

Each page can contain:
- Multiple-choice questions with options (questions with options, each explicitly labeled (1), (2), (3), (4)).
- Explanations or step-by-step solutions (clearly not options). (titled under: Explanation/Solutions/Answers or Equivalent in other languages)
- Answer keys (titled under: ANSWERS/SOLUTIONS/ANSWER KEYS or Equivalent in other languages )
- Direction texts (directions applicable to questions)
- Statements, Conclusions, or Passages (titled under: Statement/Conclusion/Passage or Equivalent in other languages कथन / निष्कर्ष / प्रस्ताव )
- Fragments of texts (heads or tails) which clearly continue from or to another page

Image-mapping tasks (performed *first*, before text extraction)
a. For every MCQ or option that contains a diagram/picture, push an entry into `"mappings"` using the required schema.
b. For every explanation containing a diagram/picture, push into `"explanationMappings"`.

- Ensure that all mathematical expressions are in LaTeX format, properly wrapped in:
     - Block: `$$ ... $$` or `\\[ ... \\]`
     - Inline: `\\( ... \\)` or `$ ... $`
     Examples:
       - Correct: \\( 4f^{14} \\)
       - Correct: \\( Ce^{2+} \\rightarrow 4f^1 \\)
       - Correct: \(\times\)

IMPORTANT RULES FOR MATH FORMAT (STRICT)
• Every mathematical expression MUST be wrapped:
   - Inline math: \\( ... \\)
   - Block math: \\[ ... \\] or $$ ... $$

• Inside JSON or quoted strings, escape every backslash:
   - Correct: "\\( \\frac{a}{b} \\)" or \\(\\times\\)
   - Wrong:   "\frac{a}{b}"  or  "\\frac{a}{b}"

• Never output raw LaTeX without wrappers. Always validate that any math expression is inside \\( ... \\) or \\[ ... \\].

• Never emit a LaTeX command like `\frac`, `\sin`, etc., unless inside a wrapped block.

Your task is to return a single JSON object in this exact structure:

{
  "directions": [
    {
      "id": "D1",
      "text": "Directions (37 – 40) : full passage text …" (empty string ⟹ stem is on another page)",
      "appliesTo": "1-8" | "37-40" | "next" | null,
      "page": "current_page_number",
      "partial": "none" | "start" | "end"
    }
  ],
  "questions": [
    {
      "question_number": "exact_question_number" | "__prev__" | "__next__",
      "type": "MCQ",
      "text": "Complete question text or empty if stem is not visible on current page",
      "passage": null,
      "options":["option 1 text", "option 2 text", "option 3 text", "option 4 text"] | null,
      "directionsId": "D1" | null,
      "page": "current_page_number",
      "partial": "none" | "start" | "end"
    }
  ],
  "answerKeys": [
    {
      "question_number": "exact_question_number",
      "answer": "(1)" | "(2)" | "(3)" | "(4)",
      "page": "current_page_number",
      "partial": "none" | "start" | "end"
    }
  ],
  "explanations": [
    {
      "explanation_number": "exact_question_number" | "__prev__" | "__next__",
      "explanation": "Full explanation text or empty if not fully visible on current page",
      "page": "current_page_number",
      "partial": "none" | "start" | "end"
    }
  ],
  "mappings": [
    {"category": "question", "question_number": exact_question_number, "option_number": null},
    {"category": "option",   "question_number": exact_question_number, "option_number": 1},
    {"category": "option",   "question_number": exact_question_number, "option_number": 2},
    {"category": "option",   "question_number": exact_question_number, "option_number": 3},
    {"category": "option",   "question_number": exact_question_number, "option_number": 4}
  ],
  "explanationMappings": [
    {"category": "explanation", "explanation_number": exact_question_number},
    {"category": "explanation", "explanation_number": exact_question_number}
  ]
}
**START-POINT RULE**  
Scan every line on the page from top to bottom. Do not drop any text simply because it appears before a question (without a question number)   
1. Treat them as an orphaned MCQ from the previous page. Emit exactly one entry in “questions” with:
{
"question_number": "__prev__",
"type": "MCQ",
"text": <all leading lines until you hit a line starting with a question number>,
"passage": null,
"options": [ <text of option 1>, <text of option 2>, <text of option 3>, <text of option 4> ],
"directionsId": null,
"page": "current_page_number",
"partial": "start"
}
- Ensure to use "__prev__" as the question_number for the orphaned MCQ.
2. If the very first line(s) consist of bullet points or “●” (dots) that look like explanation fragments (for instance, “● The charge of the Minister …”), treat them as an orphaned explanation from the previous page. Emit exactly one entry in “explanations” with:
{
"explanation_number": "__prev__",
"explanation": <all leading lines (including bullet) lines until you hit a line starting with a question number>,
"page": "current_page_number",
"partial": "start"
}
- Ensure to use "__prev__" as the explanation_number for the orphaned explanation.
3. Once you encounter either the word “Directions” (in any case) or a clearly numbered question (e.g., “4.”, “7.”, “32.”, etc.), switch back to your normal MCQ/explanation/direction capture rules.

- Each explanation typically follows the structure:
<Number>. (<Correct Option>) <Explanation Text>
• <bullet lines for further details>
- Do not mis-classify any explanation step as an MCQ.

INSTRUCTIONS:
- DO NOT summarize, paraphrase, or skip any section or content.
- DO NOT add any descriptive text, comments, or placeholders.
- DO NOT describe figures or mention missing images.
- DO NOT change, shorten, or correct the wording, even if grammatically wrong.
- Output ONLY the JSON object—no additional commentary or explanations.
Output ONLY the JSON object—no additional commentary or explanations.
"""
    return question_prompt


def explanation_extractor_prompt() -> str:
    explanation_prompt = """
You are an expert OCR and document layout model. Your task is to extract ONLY the **Explanations** of MCQs from the given image.

Each explanation typically follows this structure:
<Number>. (<Correct Option>) <Explanation Text>

For example:
4. (3) Jawaharlal Nehru wrote "The Discovery of India" during his imprisonment...
    • He was the first PM of India.
    • The book covers ancient to British India.
    (All such bullet/dot lines are part of the explanation.)

Your instructions:
- Extract **ALL such explanations** exactly as seen in the image.
- Include all parts of the explanation — even if the content is broken into multiple paragraphs or contains:
    - Bullet points (•)
    - Dots (.)
    - Sub-lists or dashed lines
- If an explanation flows into multiple lines or list format, continue capturing it as part of the same explanation.

Important:
- DO NOT summarize or skip explanations.
- DO NOT omit or reword bullet-pointed content.
- DO NOT skip explanations just because they have unusual formatting.
- DO NOT include MCQs or Answer Keys.

Treat every block that begins with a numbered pattern like `43. (2)` as an Explanation block. Continue until you reach the next number, the end of the image, or a clearly separate heading.

For text content:
- Maintain spacing, punctuation, bullets, and paragraph breaks.
- Hindi text should be copied as-is in Devanagari. No translation or transliteration.
- Do NOT correct any grammar or spelling.
- Do NOT describe any figures or illustrations.
- Do NOT insert your own words.

LaTeX Rules for math:
- Inline formulas: `$ ... $`
- Block formulas: `$$ ... $$`
- Use `\\frac{...}{...}` for fractions, especially nested ones. Match layout vertically.

Return only the extracted Explanations.
"""

    return explanation_prompt


pre_process_prompt = """
- Analyze the given image and identify which multiple-choice questions (MCQs) contain image-based objects.
- Image objects can appear in the question, answer options, or passages.
- If an image appears in the answer options, specify which options contain it.
- Ensure accurate identification.
Return the response in the below given json format:
{
    "mappings": [
        {"category": "question", "question_number": 13, "option_number": None},
        {"category": "question", "question_number": 16, "option_number": None},
        {"category": "question", "question_number": 20, "option_number": None},
        {"category": "option", "question_number": 16, "option_number": 1},
        {"category": "option", "question_number": 16, "option_number": 2},
        {"category": "option", "question_number": 16, "option_number": 3},
        {"category": "option", "question_number": 16, "option_number": 4}
    ]
}
Ensure accurate identification of all questions containing diagrams, figures, or other visual elements.
Do not include any descriptive texts.
- Output ONLY the JSON object—no additional commentary or explanations.
    - Do NOT use any markdown headings.
    - Return only JSON without ```json```
"""

pre_process_explanation_prompt = """
- Analyze the given image and identify which Explanations contain image-based objects.
- Image objects can appear in the question, answer options, or passages.
- Ensure accurate identification.
Return the response in the below given json format:
{
    "explanationMappings": [
        {"category": "explanation", "explanation_number": 13},
        {"category": "explanation", "explanation_number": 17},
    ]
}
Ensure accurate identification of all questions containing diagrams, figures, or other visual elements.
Do not include any descriptive texts.
- Output ONLY the JSON object—no additional commentary or explanations.
    - Do NOT use any markdown headings.
    - Return only JSON without ```json```
"""

def explanation_number_audit_prompt():
    return """
Carefully analyze this image and detect all explanation blocks. Be aggressive in identifying possible explanations.

Explanation blocks usually start with:
  <Number>. (<Option>)  OR  <Number>. <Text>

Instructions:
- Do NOT skip blocks just because they don’t follow exact formatting.
- Look for anything that starts with a number followed by a dot (e.g., 33. ...)
- Sub-points (•, ., -) are part of explanations.
- If some blocks are faint or broken over multiple lines, still attempt to include their numbers.

Return:
1. List of all explanation numbers present.
2. Lowest and highest number.
3. All missing numbers between.
4. Whether the first or last explanation looks cut-off or incomplete.

Return format:
{
  "found": [32, 33, 34, 36, 37, 40],
  "start": 32,
  "end": 40,
  "missing": [35, 38, 39],
  "firstLooksIncomplete": false,
  "lastLooksIncomplete": true
}
"""



def explanation_recovery_prompt(missing_str: str):
    return f"""
Please extract ONLY the explanations numbered {missing_str} from this image.

Each explanation starts with a number like 43. (2) or similar, followed by one or more lines of explanation.
The explanation may include:
- Multiple lines
- Bullet points (•), dots (.), or sub-items
- Math or formulas in LaTeX

Preserve all formatting, math, and sub-points exactly as shown in the image.
Do not include any MCQs or answer keys.
"""

def extra_fallback_explanation_prompt():
    return """
This image likely contains explanations numbered between {start} and {end}.

Please scan carefully and extract ANY explanation blocks that may have been missed previously. Look for lines starting with patterns like:

- 45. (2) Explanation text
- 47. Some explanation
- 49. • Bullet explanation

Extract all such blocks, even if the format is unusual or broken.

Do NOT return MCQs or answers.
"""

def toc_extractor_prompt():
    return """
    You are an expert OCR and document layout model. Your task is to extract the table of contents from the given image.

    Your instructions:
    - Extract the table of contents exactly as seen in the image.
    - Capture **EXTACTLY** how the chapter title and page number is written.
    - Include all parts of the table of contents.
    - Include Chapter title and page number.
    - **Include ALL the chapters with page numbers**
    - Do not include any other content from the image.
    - Do not include sub-chapters.
    - **Do not include Section titles**
    - Do not include any descriptive text.
    - Do not include any other content from the image.
    
    – Return one valid JSON object (no markdown, no wrapper text)
    OUTPUT EXAMPLE
    {
    "chapters":[
      {"title":"Chapter 1 Matter in Our Surroundings","page":"3"},
      {"title":"Chapter 2 Is Matter Around Us Pure?","page":"23"},
      {"title":"Physics and Measurement","page":"1-2"}
      {"title":"भारत और समकालीन विश्व-1","page":"1-3"}
      {"title":"भारत और समकालीन विश्व-1","page":"3-5"}
    ]
    }
    """

def mcq_text_extractor_prompt():
    return """
    You are an expert OCR and document layout model. Your task is to extract the content from the given image.
    - CRITICAL RULE:
        - **Do not infer, create, guess, generate, or assume any missing content.**
    Your instructions:
    - Extract the content exactly as seen in the image.
    - Include all parts of the content except page titles.
    - Do not include any other content from the image.
    - Do not include any descriptive text.
    - Do not include any other content from the image.
    - DO NOT include any page titles like Quantitative Reasoning (Mental/Numerical Ability) General Knowledge.
    
    IMPORTANT RULES FOR MATH FORMAT (STRICT)
    • Every mathematical expression MUST be wrapped:
       - Inline math: \\( ... \\)
       - Block math: \\[ ... \\] or $$ ... $$
    • Inside JSON or quoted strings, escape every backslash:
       - Correct: "\\( \\frac{a}{b} \\)" or \\(\\times\\)
       - Wrong:   "\frac{a}{b}"  or  "\\frac{a}{b}"
    • Never output raw LaTeX without wrappers. Always validate that any math expression is inside \\( ... \\) or \\[ ... \\].
    • Never emit a LaTeX command like `\frac`, `\sin`, etc., unless inside a wrapped block.
Output only text, no comments, no descriptive text.
- Do NOT use any markdown headings.
"""

def mcq_parser_prompt(start_question_number, end_question_number, input_text):
    return f"""
    You are an expert questions parser. Your task is to parse the given MCQs and extract the following details:
    Extract ONLY FROM {start_question_number} to {end_question_number}
    - Include "Directions and PASSAGE" only if it is mentioned like "Directions (1-5):" or "PASSAGE:"
    INPUT TEXT:
    {input_text}
    
    OUTPUT:
    Your task is to return a single valid JSON object in this exact structure:
    {{ 
        questions: [
            {{  
                "question_number": Question number,
                "Question": "Question text",
                "option1": Option 1 text,
                "option2": Option 2 text,
                "option3": Option 3 text,
                "option4": Option 4 text,
                "option5": Option 5 text, **include only if present**,
                "explanation": "(option) Full Explanation text",
                "correctAnswer": "Correct answer option example: 3",
                "directions": "Directions and PASSAGE",
                "question_images":  [],
                "option_images": [],
                "explanation_images":[]
            }} 
        ]
    }}
    - Output ONLY the JSON object—no additional commentary or explanations.
    - Do NOT use any markdown headings.
    - Return only JSON without ```json```
    """

def translator_prompt(text_to_translate, start_question_number, end_question_number, source_language, destination_language):
    return f"""
You are a professional {destination_language} translator working on an official question paper.

TASK
────
Translate each {source_language} question *stem* and its *answer options* into formal {destination_language} suitable for an academic examination (Class XI–XII level).
TRANSLATE ONLY FROM {start_question_number} to {end_question_number}
PRESERVE
────────
1. The original numbering (1, 2, 3 …) and the option markers “(1) (2) (3) (4)”.
2. All numerals (years, counts, option numbers) exactly as given.
3. Proper nouns such as “Ramsar”, “WTO”, state names and city names -– *transliterate* them in {destination_language} script, not in {source_language}.

STYLE
─────
- Use clear, standard Bangla vocabulary—not colloquial—and keep the register consistent across all questions.
- End every question stem with a {destination_language} question-mark (؟).
- Keep each option on its own line directly beneath the stem.
- Do *not* add explanations, footnotes, or extra text.
 - Output ONLY the text—no additional commentary or explanations.
    - Do NOT use any markdown headings.
    - Return only text without ```markdown``` or any other wrappers.

INPUT
─────
{text_to_translate}

- Output ONLY the text—no additional commentary or explanations.
    - Do NOT use any markdown headings.
    - Return only text without ```markdown``` or any other wrappers.
"""