#!/usr/bin/env python3
"""
Test script to verify the MCQ text extractor fixes.
"""

import os
import sys
import json
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_combine_json_files_signature():
    """Test that _combine_json_files has the correct signature."""
    try:
        from services.mcq_text_extractor_service import MCQTextExtractorService
        import inspect
        
        service = MCQTextExtractorService()
        
        # Check the signature of _combine_json_files
        sig = inspect.signature(service._combine_json_files)
        params = list(sig.parameters.keys())
        
        expected_params = ['batch_json_data', 'resource_id', 'chapter_id', 'book_id', 'request_id', 'images_from_content']
        
        # Check if all expected parameters are present
        missing_params = [p for p in expected_params if p not in params]
        extra_params = [p for p in params if p not in expected_params and p != 'self']
        
        if missing_params:
            logger.error(f"❌ Missing parameters in _combine_json_files: {missing_params}")
            return False
            
        if extra_params:
            logger.warning(f"⚠️ Extra parameters in _combine_json_files: {extra_params}")
        
        logger.info(f"✅ _combine_json_files signature correct: {params}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing _combine_json_files signature: {e}")
        return False

def test_write_text_to_s3_batch_json():
    """Test that write_text_to_s3 can handle batch_json subfolder."""
    try:
        from utils.s3_utils import write_text_to_s3
        import inspect
        
        # Check the signature
        sig = inspect.signature(write_text_to_s3)
        params = list(sig.parameters.keys())
        
        expected_params = ['content', 'book_id', 'chapter_id', 'res_id', 'file_name', 'subfolder']
        
        if all(param in params for param in expected_params):
            logger.info("✅ write_text_to_s3 has correct signature for batch_json support")
            return True
        else:
            logger.error(f"❌ write_text_to_s3 signature incorrect. Expected: {expected_params}, Got: {params}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing write_text_to_s3: {e}")
        return False

def test_batch_json_data_structure():
    """Test the batch JSON data structure handling."""
    try:
        # Simulate the batch JSON data structure
        batch_json_data = [
            ("supload/pdfextracts/123/456/789/batch_json/789_1.json", '{"questions": [{"id": 1, "text": "Question 1"}]}'),
            ("789_2.json", '{"questions": [{"id": 2, "text": "Question 2"}]}')
        ]
        
        # Test processing the data structure
        combined_questions = []
        
        for i, (batch_path_or_filename, batch_content) in enumerate(batch_json_data):
            try:
                # Extract filename for logging (could be S3 path or just filename)
                if batch_path_or_filename.startswith('supload/'):
                    # It's an S3 path, extract filename
                    batch_display_name = os.path.basename(batch_path_or_filename)
                else:
                    # It's just a filename
                    batch_display_name = batch_path_or_filename
                
                # Parse JSON content
                batch_json = json.loads(batch_content)

                # Extract questions array
                if "questions" in batch_json and isinstance(batch_json["questions"], list):
                    combined_questions.extend(batch_json["questions"])
                    logger.info(f"✅ Added {len(batch_json['questions'])} questions from {batch_display_name}")
                else:
                    logger.warning(f"⚠️ No 'questions' array found in {batch_display_name}")

            except json.JSONDecodeError as e:
                logger.error(f"❌ JSON decode error in {batch_display_name}: {e}")
                return False
            except Exception as e:
                logger.error(f"❌ Error processing {batch_display_name}: {e}")
                return False
        
        if len(combined_questions) == 2:
            logger.info("✅ Batch JSON data structure processing works correctly")
            return True
        else:
            logger.error(f"❌ Expected 2 questions, got {len(combined_questions)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing batch JSON data structure: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("🚀 Starting MCQ text extractor fixes tests...")
    
    tests = [
        ("_combine_json_files Signature", test_combine_json_files_signature),
        ("write_text_to_s3 Batch JSON Support", test_write_text_to_s3_batch_json),
        ("Batch JSON Data Structure", test_batch_json_data_structure),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All MCQ fixes tests passed!")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
