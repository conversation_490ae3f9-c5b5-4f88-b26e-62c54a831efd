#!/usr/bin/env python3
"""
Basic test script to verify S3 utility functions work correctly.
"""

import os
import sys
import tempfile
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_s3_utils_import():
    """Test that S3 utils can be imported."""
    try:
        # Add the project root to the Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from utils.s3_utils import write_text_to_s3, read_file_from_s3, get_s3_path
        logger.info("✅ Successfully imported S3 utilities")
        return True
    except ImportError as e:
        logger.error(f"❌ Failed to import S3 utilities: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error importing S3 utilities: {e}")
        return False

def test_config_import():
    """Test that config can be imported."""
    try:
        import config
        logger.info("✅ Successfully imported config")
        
        # Check if S3 configuration exists
        if hasattr(config, 'S3_MOUNT_PATH'):
            logger.info(f"✅ S3_MOUNT_PATH configured: {config.S3_MOUNT_PATH}")
        else:
            logger.warning("⚠️ S3_MOUNT_PATH not found in config")
            
        if hasattr(config, 'S3_USE_SUDO'):
            logger.info(f"✅ S3_USE_SUDO configured: {config.S3_USE_SUDO}")
        else:
            logger.warning("⚠️ S3_USE_SUDO not found in config")
            
        return True
    except ImportError as e:
        logger.error(f"❌ Failed to import config: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Error importing config: {e}")
        return False

def test_file_structure():
    """Test that the migrated files have the expected structure."""
    try:
        # Check if the migrated files exist
        files_to_check = [
            'agents/pdf_text_extractor.py',
            'services/mcq_text_extractor_service.py',
            'utils/s3_utils.py'
        ]
        
        all_exist = True
        for file_path in files_to_check:
            if os.path.exists(file_path):
                logger.info(f"✅ Found migrated file: {file_path}")
            else:
                logger.error(f"❌ Missing migrated file: {file_path}")
                all_exist = False
                
        return all_exist
    except Exception as e:
        logger.error(f"❌ Error checking file structure: {e}")
        return False

def test_s3_utils_functions():
    """Test that S3 utility functions are properly defined."""
    try:
        from utils.s3_utils import write_text_to_s3, read_file_from_s3, get_s3_path, list_files_in_s3_directory
        
        # Check function signatures
        import inspect
        
        # Test write_text_to_s3 signature
        sig = inspect.signature(write_text_to_s3)
        expected_params = ['content', 'book_id', 'chapter_id', 'res_id', 'file_name', 'subfolder']
        actual_params = list(sig.parameters.keys())
        
        if all(param in actual_params for param in expected_params):
            logger.info("✅ write_text_to_s3 has correct signature")
        else:
            logger.error(f"❌ write_text_to_s3 signature mismatch. Expected: {expected_params}, Got: {actual_params}")
            return False
            
        # Test other functions exist
        functions_to_check = [
            ('read_file_from_s3', ['s3_file_path']),
            ('get_s3_path', ['relative_path']),
            ('list_files_in_s3_directory', ['book_id', 'chapter_id', 'res_id'])
        ]
        
        for func_name, expected_params in functions_to_check:
            func = globals().get(func_name) or locals().get(func_name)
            if func_name == 'read_file_from_s3':
                func = read_file_from_s3
            elif func_name == 'get_s3_path':
                func = get_s3_path
            elif func_name == 'list_files_in_s3_directory':
                func = list_files_in_s3_directory
                
            if func:
                sig = inspect.signature(func)
                actual_params = list(sig.parameters.keys())
                if all(param in actual_params for param in expected_params):
                    logger.info(f"✅ {func_name} has correct signature")
                else:
                    logger.warning(f"⚠️ {func_name} signature may have changed. Expected: {expected_params}, Got: {actual_params}")
            else:
                logger.error(f"❌ Function {func_name} not found")
                return False
                
        return True
    except Exception as e:
        logger.error(f"❌ Error testing S3 utility functions: {e}")
        return False

def main():
    """Run all basic tests."""
    logger.info("🚀 Starting basic S3 migration tests...")
    
    tests = [
        ("Config Import", test_config_import),
        ("S3 Utils Import", test_s3_utils_import),
        ("File Structure", test_file_structure),
        ("S3 Utils Functions", test_s3_utils_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All basic tests passed! S3 migration structure is correct.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
