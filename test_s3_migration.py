#!/usr/bin/env python3
"""
Test script to verify S3 migration functionality for PDF text extraction and MCQ text extraction.
"""

import asyncio
import logging
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.s3_utils import write_text_to_s3, read_file_from_s3, get_s3_path
from agents.pdf_text_extractor import PDFTextExtractor
from services.mcq_text_extractor_service import MCQTextExtractorService

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_s3_text_operations():
    """Test basic S3 text operations."""
    logger.info("Testing S3 text operations...")
    
    # Test data
    test_content = "This is a test content for S3 migration.\nLine 2 of test content."
    book_id = "test_book_123"
    chapter_id = "test_chapter_456"
    res_id = "test_resource_789"
    file_name = "test_file.txt"
    
    try:
        # Test writing to S3
        logger.info("Testing write_text_to_s3...")
        s3_path = write_text_to_s3(
            content=test_content,
            book_id=book_id,
            chapter_id=chapter_id,
            res_id=res_id,
            file_name=file_name,
            subfolder="test_extraction"
        )
        
        if s3_path:
            logger.info(f"✅ Successfully wrote to S3: {s3_path}")
            
            # Test reading from S3
            logger.info("Testing read_file_from_s3...")
            full_s3_path = get_s3_path(s3_path)
            content = read_file_from_s3(full_s3_path)
            
            if content:
                content_str = content.decode('utf-8')
                if content_str == test_content:
                    logger.info("✅ Successfully read from S3 and content matches")
                    return True
                else:
                    logger.error(f"❌ Content mismatch. Expected: {test_content}, Got: {content_str}")
                    return False
            else:
                logger.error("❌ Failed to read content from S3")
                return False
        else:
            logger.error("❌ Failed to write to S3")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error during S3 operations test: {e}")
        return False


async def test_pdf_text_extractor_s3():
    """Test PDFTextExtractor with S3 storage."""
    logger.info("Testing PDFTextExtractor S3 integration...")
    
    try:
        extractor = PDFTextExtractor()
        
        # Test with a sample chapter ID (this would normally trigger the full extraction process)
        # For testing, we'll just verify the class can be instantiated and has the right methods
        
        if hasattr(extractor, 'process_pdf_text_extraction_by_chapter'):
            logger.info("✅ PDFTextExtractor has required methods for S3 integration")
            return True
        else:
            logger.error("❌ PDFTextExtractor missing required methods")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing PDFTextExtractor: {e}")
        return False


async def test_mcq_text_extractor_s3():
    """Test MCQTextExtractorService with S3 storage."""
    logger.info("Testing MCQTextExtractorService S3 integration...")
    
    try:
        service = MCQTextExtractorService()
        
        # Test with sample data to verify the service can be instantiated
        if hasattr(service, 'extract_text_from_resource'):
            logger.info("✅ MCQTextExtractorService has required methods for S3 integration")
            return True
        else:
            logger.error("❌ MCQTextExtractorService missing required methods")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing MCQTextExtractorService: {e}")
        return False


async def main():
    """Run all tests."""
    logger.info("🚀 Starting S3 migration tests...")
    
    tests = [
        ("S3 Text Operations", test_s3_text_operations()),
        ("PDFTextExtractor S3 Integration", test_pdf_text_extractor_s3()),
        ("MCQTextExtractorService S3 Integration", test_mcq_text_extractor_s3()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        try:
            result = await test_coro
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name}: PASSED")
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n📊 Test Summary:")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! S3 migration is ready.")
        return 0
    else:
        logger.error("💥 Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
